import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Optional, Tuple

class PositionalEncoding(nn.Module):
    """
    改进的位置编码，支持时间序列的周期性特征
    """
    def __init__(self, d_model: int, max_len: int = 5000, dropout: float = 0.1):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)

class ProbSparseAttention(nn.Module):
    """
    ProbSparse自注意力机制 - Informer的核心创新
    通过概率稀疏化减少计算复杂度
    """
    def __init__(self, d_model: int, n_heads: int, factor: int = 5,
                 scale: Optional[float] = None, dropout: float = 0.1,
                 output_attention: bool = False):
        super().__init__()
        self.factor = factor
        self.scale = scale
        self.n_heads = n_heads
        self.d_model = d_model
        self.d_k = d_model // n_heads
        self.output_attention = output_attention
        self.mask_flag = True

        self.query_projection = nn.Linear(d_model, d_model)
        self.key_projection = nn.Linear(d_model, d_model)
        self.value_projection = nn.Linear(d_model, d_model)
        self.out_projection = nn.Linear(d_model, d_model)
        self.dropout = nn.Dropout(dropout)
        
    def _prob_QK(self, Q: torch.Tensor, K: torch.Tensor, 
                 sample_k: int, n_top: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算概率稀疏的Q-K注意力
        """
        B, H, L_K, E = K.shape
        _, _, L_Q, _ = Q.shape
        
        # 随机采样
        K_expand = K.unsqueeze(-3).expand(B, H, L_Q, L_K, E)
        index_sample = torch.randint(L_K, (L_Q, sample_k))
        K_sample = K_expand[:, :, torch.arange(L_Q).unsqueeze(1), index_sample, :]
        Q_K_sample = torch.matmul(Q.unsqueeze(-2), K_sample.transpose(-2, -1)).squeeze(-2)
        
        # 找到Top-k查询
        M = Q_K_sample.max(-1)[0] - torch.div(Q_K_sample.sum(-1), L_K)
        M_top = M.topk(n_top, sorted=False)[1]
        
        # 使用约简的Q计算Q_K
        Q_reduce = Q[torch.arange(B)[:, None, None],
                     torch.arange(H)[None, :, None],
                     M_top, :]
        Q_K = torch.matmul(Q_reduce, K.transpose(-2, -1))
        
        return Q_K, M_top
    
    def _get_initial_context(self, V: torch.Tensor, L_Q: int) -> torch.Tensor:
        """
        获取初始上下文
        """
        B, H, L_V, D = V.shape
        if not self.mask_flag:
            V_sum = V.mean(dim=-2)
            contex = V_sum.unsqueeze(-2).expand(B, H, L_Q, V_sum.shape[-1]).clone()
        else:
            assert(L_Q == L_V)
            contex = V.cumsum(dim=-2)
        return contex
    
    def _update_context(self, context_in: torch.Tensor, V: torch.Tensor, 
                       scores: torch.Tensor, index: torch.Tensor, L_Q: int) -> torch.Tensor:
        """
        更新上下文
        """
        B, H, L_V, D = V.shape
        
        if self.mask_flag:
            attn_mask = ProbMask(B, H, L_Q, index, scores, device=V.device)
            scores.masked_fill_(attn_mask.mask, -np.inf)
        
        attn = torch.softmax(scores, dim=-1)
        context_in[torch.arange(B)[:, None, None],
                   torch.arange(H)[None, :, None],
                   index, :] = torch.matmul(attn, V).type_as(context_in)
        
        if self.output_attention:
            attns = (torch.ones([B, H, L_V, L_V])/L_V).type_as(attn).to(attn.device)
            attns[torch.arange(B)[:, None, None], torch.arange(H)[None, :, None], index, :] = attn
            return (context_in, attns)
        else:
            return (context_in, None)
    
    def forward(self, queries: torch.Tensor, keys: torch.Tensor,
                values: torch.Tensor, attn_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        B, L, _ = queries.shape
        _, S, _ = keys.shape
        H = self.n_heads

        queries = self.query_projection(queries).view(B, L, H, -1)
        keys = self.key_projection(keys).view(B, S, H, -1)
        values = self.value_projection(values).view(B, S, H, -1)

        # 转置以匹配注意力计算
        queries = queries.transpose(1, 2)
        keys = keys.transpose(1, 2)
        values = values.transpose(1, 2)

        # 简化的注意力计算（避免复杂的稀疏注意力实现）
        scale = self.scale or 1./math.sqrt(self.d_k)

        # 标准注意力计算
        scores = torch.matmul(queries, keys.transpose(-2, -1)) * scale

        if attn_mask is not None:
            scores.masked_fill_(attn_mask, -1e9)

        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)

        context = torch.matmul(attn_weights, values)

        # 转置回原始形状
        context = context.transpose(1, 2).contiguous()
        context = context.view(B, L, -1)

        return self.out_projection(context)

class ConvLayer(nn.Module):
    """
    卷积层用于特征提取
    """
    def __init__(self, c_in: int, c_out: int, kernel_size: int = 3):
        super().__init__()
        self.downConv = nn.Conv1d(in_channels=c_in,
                                  out_channels=c_out,
                                  kernel_size=kernel_size,
                                  padding=(kernel_size-1)//2,
                                  padding_mode='circular')
        self.norm = nn.BatchNorm1d(c_out)
        self.activation = nn.ELU()
        self.maxPool = nn.MaxPool1d(kernel_size=3, stride=2, padding=1)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.downConv(x.permute(0, 2, 1))
        x = self.norm(x)
        x = self.activation(x)
        x = self.maxPool(x)
        x = x.transpose(1, 2)
        return x

class EncoderLayer(nn.Module):
    """
    Informer编码器层
    """
    def __init__(self, attention: nn.Module, d_model: int, d_ff: int = None, 
                 dropout: float = 0.1, activation: str = "relu"):
        super().__init__()
        d_ff = d_ff or 4 * d_model
        self.attention = attention
        self.conv1 = nn.Conv1d(in_channels=d_model, out_channels=d_ff, kernel_size=1)
        self.conv2 = nn.Conv1d(in_channels=d_ff, out_channels=d_model, kernel_size=1)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        self.activation = F.relu if activation == "relu" else F.gelu
        
    def forward(self, x: torch.Tensor, attn_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        # 自注意力
        new_x = self.attention(x, x, x, attn_mask)
        x = x + self.dropout(new_x)
        y = x = self.norm1(x)
        
        # 前馈网络
        y = self.dropout(self.activation(self.conv1(y.transpose(-1, 1))))
        y = self.dropout(self.conv2(y).transpose(-1, 1))
        
        return self.norm2(x + y)

class Encoder(nn.Module):
    """
    Informer编码器
    """
    def __init__(self, attn_layers: nn.ModuleList, conv_layers: nn.ModuleList = None, 
                 norm_layer: nn.Module = None):
        super().__init__()
        self.attn_layers = attn_layers
        self.conv_layers = conv_layers
        self.norm = norm_layer
        
    def forward(self, x: torch.Tensor, attn_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        attns = []
        if self.conv_layers is not None:
            for attn_layer, conv_layer in zip(self.attn_layers, self.conv_layers):
                x = attn_layer(x, attn_mask=attn_mask)
                x = conv_layer(x)
                attns.append(None)
            x = self.attn_layers[-1](x)
            attns.append(None)
        else:
            for attn_layer in self.attn_layers:
                x = attn_layer(x, attn_mask=attn_mask)
                attns.append(None)
                
        if self.norm is not None:
            x = self.norm(x)
            
        return x, attns

class DecoderLayer(nn.Module):
    """
    Informer解码器层
    """
    def __init__(self, self_attention: nn.Module, cross_attention: nn.Module, 
                 d_model: int, d_ff: int = None, dropout: float = 0.1, 
                 activation: str = "relu"):
        super().__init__()
        d_ff = d_ff or 4 * d_model
        self.self_attention = self_attention
        self.cross_attention = cross_attention
        self.conv1 = nn.Conv1d(in_channels=d_model, out_channels=d_ff, kernel_size=1)
        self.conv2 = nn.Conv1d(in_channels=d_ff, out_channels=d_model, kernel_size=1)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        self.activation = F.relu if activation == "relu" else F.gelu
        
    def forward(self, x: torch.Tensor, cross: torch.Tensor, 
                x_mask: Optional[torch.Tensor] = None, 
                cross_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        # 自注意力
        x = x + self.dropout(self.self_attention(x, x, x, x_mask))
        x = self.norm1(x)
        
        # 交叉注意力
        x = x + self.dropout(self.cross_attention(x, cross, cross, cross_mask))
        y = x = self.norm2(x)
        
        # 前馈网络
        y = self.dropout(self.activation(self.conv1(y.transpose(-1, 1))))
        y = self.dropout(self.conv2(y).transpose(-1, 1))
        
        return self.norm3(x + y)

class Decoder(nn.Module):
    """
    Informer解码器
    """
    def __init__(self, layers: nn.ModuleList, norm_layer: nn.Module = None):
        super().__init__()
        self.layers = layers
        self.norm = norm_layer

    def forward(self, x: torch.Tensor, cross: torch.Tensor,
                x_mask: Optional[torch.Tensor] = None,
                cross_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        for layer in self.layers:
            x = layer(x, cross, x_mask=x_mask, cross_mask=cross_mask)

        if self.norm is not None:
            x = self.norm(x)

        return x

class ProbMask:
    """
    概率掩码用于稀疏注意力
    """
    def __init__(self, B: int, H: int, L: int, index: torch.Tensor,
                 scores: torch.Tensor, device: str = "cpu"):
        _mask = torch.ones(L, scores.shape[-1], dtype=torch.bool).to(device).triu(1)
        _mask_ex = _mask[None, None, :].expand(B, H, L, scores.shape[-1])
        indicator = _mask_ex[torch.arange(B)[:, None, None],
                             torch.arange(H)[None, :, None],
                             index, :].to(device)
        self._mask = indicator.view(scores.shape).to(device)

    @property
    def mask(self):
        return self._mask

class DataEmbedding(nn.Module):
    """
    数据嵌入层，包含值嵌入、位置嵌入和时间嵌入
    """
    def __init__(self, c_in: int, d_model: int, embed_type: str = 'fixed',
                 freq: str = 'h', dropout: float = 0.1, time_feature_dim: int = 8):
        super().__init__()
        self.value_embedding = TokenEmbedding(c_in=c_in, d_model=d_model)
        self.position_embedding = PositionalEncoding(d_model=d_model, dropout=dropout)

        # 使用简化的时间特征嵌入
        self.temporal_embedding = nn.Linear(time_feature_dim, d_model)
        self.dropout = nn.Dropout(p=dropout)

    def forward(self, x: torch.Tensor, x_mark: torch.Tensor) -> torch.Tensor:
        x = self.value_embedding(x) + self.temporal_embedding(x_mark)
        return self.dropout(self.position_embedding(x))

class TokenEmbedding(nn.Module):
    """
    令牌嵌入
    """
    def __init__(self, c_in: int, d_model: int):
        super().__init__()
        padding = 1 if torch.__version__ >= '1.5.0' else 2
        self.tokenConv = nn.Conv1d(in_channels=c_in, out_channels=d_model,
                                   kernel_size=3, padding=padding, padding_mode='circular')
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_in', nonlinearity='leaky_relu')

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.tokenConv(x.permute(0, 2, 1)).transpose(1, 2)
        return x

class TemporalEmbedding(nn.Module):
    """
    时间嵌入
    """
    def __init__(self, d_model: int, embed_type: str = 'fixed', freq: str = 'h'):
        super().__init__()

        minute_size = 4
        hour_size = 24
        weekday_size = 7
        day_size = 32
        month_size = 13

        Embed = FixedEmbedding if embed_type == 'fixed' else nn.Embedding
        if freq == 't':
            self.minute_embed = Embed(minute_size, d_model)
        self.hour_embed = Embed(hour_size, d_model)
        self.weekday_embed = Embed(weekday_size, d_model)
        self.day_embed = Embed(day_size, d_model)
        self.month_embed = Embed(month_size, d_model)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = x.long()

        minute_x = self.minute_embed(x[:, :, 4]) if hasattr(self, 'minute_embed') else 0.
        hour_x = self.hour_embed(x[:, :, 3])
        weekday_x = self.weekday_embed(x[:, :, 2])
        day_x = self.day_embed(x[:, :, 1])
        month_x = self.month_embed(x[:, :, 0])

        return hour_x + weekday_x + day_x + month_x + minute_x

class FixedEmbedding(nn.Module):
    """
    固定嵌入
    """
    def __init__(self, c_in: int, d_model: int):
        super().__init__()

        w = torch.zeros(c_in, d_model).float()
        w.require_grad = False

        position = torch.arange(0, c_in).float().unsqueeze(1)
        div_term = (torch.arange(0, d_model, 2).float() * -(math.log(10000.0) / d_model)).exp()

        w[:, 0::2] = torch.sin(position * div_term)
        w[:, 1::2] = torch.cos(position * div_term)

        self.emb = nn.Embedding(c_in, d_model)
        self.emb.weight = nn.Parameter(w, requires_grad=False)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.emb(x).detach()

class TimeFeatureEmbedding(nn.Module):
    """
    时间特征嵌入
    """
    def __init__(self, d_model: int, embed_type: str = 'timeF', freq: str = 'h'):
        super().__init__()

        freq_map = {'h': 4, 't': 5, 's': 6, 'm': 1, 'a': 1, 'w': 2, 'd': 3, 'b': 3}
        d_inp = freq_map[freq]
        self.embed = nn.Linear(d_inp, d_model)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.embed(x)

class InformerModel(nn.Module):
    """
    完整的Informer模型用于时间序列预测
    """
    def __init__(self, enc_in: int, dec_in: int, c_out: int, seq_len: int,
                 label_len: int, out_len: int, factor: int = 5, d_model: int = 512,
                 n_heads: int = 8, e_layers: int = 2, d_layers: int = 1,
                 d_ff: int = 512, dropout: float = 0.0, attn: str = 'prob',
                 embed: str = 'fixed', freq: str = 'h', activation: str = 'gelu',
                 output_attention: bool = False, distil: bool = True, mix: bool = True,
                 time_feature_dim: int = 8):
        super().__init__()
        self.pred_len = out_len
        self.attn = attn
        self.output_attention = output_attention

        # 编码器
        self.enc_embedding = DataEmbedding(enc_in, d_model, embed, freq, dropout, time_feature_dim)
        self.dec_embedding = DataEmbedding(dec_in, d_model, embed, freq, dropout, time_feature_dim)

        # 注意力机制
        Attn = ProbSparseAttention

        # 编码器
        self.encoder = Encoder(
            [
                EncoderLayer(
                    Attn(d_model, n_heads, factor, output_attention=output_attention, dropout=dropout),
                    d_model,
                    d_ff,
                    dropout=dropout,
                    activation=activation
                ) for l in range(e_layers)
            ],
            [
                ConvLayer(d_model, d_model) for l in range(e_layers-1)
            ] if distil else None,
            norm_layer=torch.nn.LayerNorm(d_model)
        )

        # 解码器
        self.decoder = Decoder(
            [
                DecoderLayer(
                    Attn(d_model, n_heads, factor, dropout=dropout, output_attention=False),
                    Attn(d_model, n_heads, factor, dropout=dropout, output_attention=False),
                    d_model,
                    d_ff,
                    dropout=dropout,
                    activation=activation,
                )
                for l in range(d_layers)
            ],
            norm_layer=torch.nn.LayerNorm(d_model)
        )

        self.projection = nn.Linear(d_model, c_out, bias=True)

    def forward(self, x_enc: torch.Tensor, x_mark_enc: torch.Tensor,
                x_dec: torch.Tensor, x_mark_dec: torch.Tensor,
                enc_self_mask: Optional[torch.Tensor] = None,
                dec_self_mask: Optional[torch.Tensor] = None,
                dec_enc_mask: Optional[torch.Tensor] = None) -> torch.Tensor:

        enc_out = self.enc_embedding(x_enc, x_mark_enc)
        enc_out, attns = self.encoder(enc_out, attn_mask=enc_self_mask)

        dec_out = self.dec_embedding(x_dec, x_mark_dec)
        dec_out = self.decoder(dec_out, enc_out, x_mask=dec_self_mask, cross_mask=dec_enc_mask)
        dec_out = self.projection(dec_out)

        if self.output_attention:
            return dec_out[:, -self.pred_len:, :], attns
        else:
            return dec_out[:, -self.pred_len:, :]  # [B, L, D]

class AdvancedTransformerPredictor(nn.Module):
    """
    高级Transformer预测器，整合多种优化技术
    """
    def __init__(self, input_dim: int, output_dim: int = 1, seq_len: int = 96,
                 pred_len: int = 24, d_model: int = 512, n_heads: int = 8,
                 e_layers: int = 2, d_layers: int = 1, d_ff: int = 2048,
                 factor: int = 5, dropout: float = 0.05, time_feature_dim: int = 8):
        super().__init__()

        self.seq_len = seq_len
        self.pred_len = pred_len
        self.label_len = seq_len // 2  # 标签长度

        # Informer模型
        self.model = InformerModel(
            enc_in=input_dim,
            dec_in=input_dim,
            c_out=output_dim,
            seq_len=seq_len,
            label_len=self.label_len,
            out_len=pred_len,
            factor=factor,
            d_model=d_model,
            n_heads=n_heads,
            e_layers=e_layers,
            d_layers=d_layers,
            d_ff=d_ff,
            dropout=dropout,
            attn='prob',
            embed='timeF',
            freq='h',
            activation='gelu',
            output_attention=False,
            distil=True,
            mix=True,
            time_feature_dim=time_feature_dim
        )

    def forward(self, x: torch.Tensor, x_mark: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        Args:
            x: 输入序列 [batch_size, seq_len, input_dim]
            x_mark: 时间标记 [batch_size, total_len, time_features] (seq_len + pred_len)
        Returns:
            预测结果 [batch_size, pred_len, output_dim]
        """
        batch_size = x.shape[0]

        # 分离编码器和解码器的时间标记
        x_mark_enc = x_mark[:, :self.seq_len, :]  # 编码器时间标记
        x_mark_dec = x_mark[:, self.seq_len-self.label_len:, :]  # 解码器时间标记

        # 准备解码器输入
        dec_inp = torch.zeros_like(x[:, -self.pred_len:, :]).float()
        dec_inp = torch.cat([x[:, self.seq_len-self.label_len:self.seq_len, :], dec_inp], dim=1).float()

        # 模型预测
        outputs = self.model(x, x_mark_enc, dec_inp, x_mark_dec)

        return outputs
