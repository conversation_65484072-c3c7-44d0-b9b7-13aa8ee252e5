import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_preprocess_data(file_path):
    """
    加载和预处理风机数据
    """
    # 尝试不同的编码方式读取CSV文件
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
    df = None
    
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用 {encoding} 编码读取文件")
            break
        except UnicodeDecodeError:
            continue
    
    if df is None:
        raise ValueError("无法读取文件，请检查文件编码")
    
    # 显示原始列名
    print("原始列名:", df.columns.tolist())
    
    # 重命名列（处理中文列名）
    column_mapping = {
        df.columns[0]: 'timestamp',  # 采样时刻
        df.columns[1]: 'wind_speed',  # 风速(m/s)
        df.columns[2]: 'power',       # 功率(kw) - 目标变量
        df.columns[3]: 'rotor_speed', # 发电机转速(rpm)
        df.columns[4]: 'blade_angle', # 叶片1角度(度)
        df.columns[5]: 'energy_total' # 累计电量(kwh)
    }
    
    df = df.rename(columns=column_mapping)
    print("重命名后的列名:", df.columns.tolist())
    
    # 转换时间戳
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    # 设置时间戳为索引
    df.set_index('timestamp', inplace=True)
    
    # 数据类型转换
    numeric_columns = ['wind_speed', 'power', 'rotor_speed', 'blade_angle', 'energy_total']
    for col in numeric_columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    return df

def explore_data(df):
    """
    探索性数据分析
    """
    print("=" * 50)
    print("数据基本信息")
    print("=" * 50)
    print(f"数据形状: {df.shape}")
    print(f"时间范围: {df.index.min()} 到 {df.index.max()}")
    print(f"采样频率: {df.index.freq}")
    
    print("\n数据统计信息:")
    print(df.describe())
    
    print("\n缺失值统计:")
    print(df.isnull().sum())
    
    # 检查异常值
    print("\n异常值检测 (使用IQR方法):")
    for col in df.select_dtypes(include=[np.number]).columns:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
        print(f"{col}: {len(outliers)} 个异常值 ({len(outliers)/len(df)*100:.2f}%)")

def visualize_data(df):
    """
    数据可视化
    """
    fig, axes = plt.subplots(3, 2, figsize=(15, 12))
    fig.suptitle('风机数据探索性分析', fontsize=16)
    
    # 功率时间序列图
    axes[0, 0].plot(df.index, df['power'], alpha=0.7)
    axes[0, 0].set_title('功率时间序列')
    axes[0, 0].set_ylabel('功率 (kW)')
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # 功率分布直方图
    axes[0, 1].hist(df['power'], bins=50, alpha=0.7, edgecolor='black')
    axes[0, 1].set_title('功率分布')
    axes[0, 1].set_xlabel('功率 (kW)')
    axes[0, 1].set_ylabel('频次')
    
    # 风速vs功率散点图
    axes[1, 0].scatter(df['wind_speed'], df['power'], alpha=0.5)
    axes[1, 0].set_title('风速 vs 功率')
    axes[1, 0].set_xlabel('风速 (m/s)')
    axes[1, 0].set_ylabel('功率 (kW)')
    
    # 转速vs功率散点图
    axes[1, 1].scatter(df['rotor_speed'], df['power'], alpha=0.5)
    axes[1, 1].set_title('转速 vs 功率')
    axes[1, 1].set_xlabel('转速 (rpm)')
    axes[1, 1].set_ylabel('功率 (kW)')
    
    # 叶片角度时间序列
    axes[2, 0].plot(df.index, df['blade_angle'], alpha=0.7, color='green')
    axes[2, 0].set_title('叶片角度时间序列')
    axes[2, 0].set_ylabel('角度 (度)')
    axes[2, 0].tick_params(axis='x', rotation=45)
    
    # 相关性热力图
    corr_matrix = df.select_dtypes(include=[np.number]).corr()
    im = axes[2, 1].imshow(corr_matrix, cmap='coolwarm', aspect='auto')
    axes[2, 1].set_title('特征相关性热力图')
    axes[2, 1].set_xticks(range(len(corr_matrix.columns)))
    axes[2, 1].set_yticks(range(len(corr_matrix.columns)))
    axes[2, 1].set_xticklabels(corr_matrix.columns, rotation=45)
    axes[2, 1].set_yticklabels(corr_matrix.columns)
    
    # 添加相关系数文本
    for i in range(len(corr_matrix.columns)):
        for j in range(len(corr_matrix.columns)):
            text = axes[2, 1].text(j, i, f'{corr_matrix.iloc[i, j]:.2f}',
                                 ha="center", va="center", color="black", fontsize=8)
    
    plt.tight_layout()
    plt.savefig('data_exploration.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_time_features(df):
    """
    创建时间特征
    """
    df_features = df.copy()
    
    # 提取时间特征
    df_features['hour'] = df_features.index.hour
    df_features['day'] = df_features.index.day
    df_features['month'] = df_features.index.month
    df_features['dayofweek'] = df_features.index.dayofweek
    df_features['dayofyear'] = df_features.index.dayofyear
    
    # 周期性编码
    df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
    df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)
    df_features['day_sin'] = np.sin(2 * np.pi * df_features['day'] / 31)
    df_features['day_cos'] = np.cos(2 * np.pi * df_features['day'] / 31)
    df_features['month_sin'] = np.sin(2 * np.pi * df_features['month'] / 12)
    df_features['month_cos'] = np.cos(2 * np.pi * df_features['month'] / 12)
    
    return df_features

def clean_data(df):
    """
    数据清洗
    """
    df_clean = df.copy()
    
    # 处理缺失值
    df_clean = df_clean.fillna(method='ffill').fillna(method='bfill')
    
    # 移除极端异常值（使用3σ原则）
    for col in df_clean.select_dtypes(include=[np.number]).columns:
        mean = df_clean[col].mean()
        std = df_clean[col].std()
        df_clean = df_clean[
            (df_clean[col] >= mean - 3*std) & 
            (df_clean[col] <= mean + 3*std)
        ]
    
    return df_clean

if __name__ == "__main__":
    # 加载数据
    file_path = "2021年1月1期01风机数据.csv"
    df = load_and_preprocess_data(file_path)
    
    # 探索性数据分析
    explore_data(df)
    
    # 数据可视化
    visualize_data(df)
    
    # 数据清洗
    df_clean = clean_data(df)
    print(f"\n清洗后数据形状: {df_clean.shape}")
    
    # 创建时间特征
    df_features = create_time_features(df_clean)
    print(f"添加时间特征后数据形状: {df_features.shape}")
    print("新增特征:", [col for col in df_features.columns if col not in df.columns])
    
    # 保存预处理后的数据
    df_features.to_csv('preprocessed_wind_data.csv')
    print("\n预处理完成，数据已保存到 'preprocessed_wind_data.csv'")
