import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class WindPowerDataset(Dataset):
    """风机功率预测数据集"""
    def __init__(self, data: np.ndarray, seq_len: int = 48, pred_len: int = 12, target_col: int = 1):
        self.data = data
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.target_col = target_col
        
    def __len__(self):
        return len(self.data) - self.seq_len - self.pred_len + 1
    
    def __getitem__(self, idx):
        # 输入序列
        seq_x = self.data[idx:idx + self.seq_len]
        seq_y = self.data[idx + self.seq_len:idx + self.seq_len + self.pred_len, self.target_col]
        
        return {
            'seq_x': torch.FloatTensor(seq_x),
            'seq_y': torch.FloatTensor(seq_y)
        }

class WindPowerTransformer(nn.Module):
    """专门为风机功率预测设计的Transformer模型"""
    def __init__(self, input_dim: int, d_model: int = 128, n_heads: int = 4, 
                 n_layers: int = 3, seq_len: int = 48, pred_len: int = 12, 
                 dropout: float = 0.1):
        super().__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # 输入投影
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(seq_len, d_model) * 0.1)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True,
            norm_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 输出层
        self.output_projection = nn.Sequential(
            nn.Linear(d_model, d_model * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 2, d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, pred_len)
        )
        
        # 残差连接
        self.residual_projection = nn.Linear(input_dim, pred_len)
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        batch_size = x.size(0)
        
        # 输入投影
        x_proj = self.input_projection(x)
        
        # 位置编码
        x_proj = x_proj + self.pos_encoding.unsqueeze(0)
        
        # Transformer编码
        transformer_out = self.transformer(x_proj)
        
        # 全局平均池化
        pooled = transformer_out.mean(dim=1)
        
        # 主要输出
        main_output = self.output_projection(pooled)
        
        # 残差连接（使用最后一个时间步的功率值）
        last_power = x[:, -1, 1]  # 假设功率是第二列
        residual = last_power.unsqueeze(1).repeat(1, self.pred_len)
        
        # 组合输出
        output = main_output + 0.1 * residual
        
        return output.unsqueeze(-1)

class WindPowerPredictor:
    """风机功率预测器"""
    def __init__(self, seq_len: int = 48, pred_len: int = 12, d_model: int = 128, 
                 n_heads: int = 4, n_layers: int = 3, dropout: float = 0.1,
                 learning_rate: float = 0.001, batch_size: int = 64):
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        
        # 设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 数据预处理器
        self.feature_scaler = MinMaxScaler()
        
        # 模型参数
        self.model_params = {
            'd_model': d_model,
            'n_heads': n_heads,
            'n_layers': n_layers,
            'seq_len': seq_len,
            'pred_len': pred_len,
            'dropout': dropout
        }
        
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.criterion = nn.MSELoss()
    
    def prepare_data(self, df: pd.DataFrame, target_col: str = 'power', 
                     train_ratio: float = 0.7, val_ratio: float = 0.2):
        """准备数据"""
        # 选择特征列
        feature_cols = ['wind_speed', 'power', 'rotor_speed', 'blade_angle']
        
        # 确保所有需要的列都存在
        available_features = [col for col in feature_cols if col in df.columns]
        print(f"可用特征: {available_features}")
        
        # 提取特征
        features = df[available_features].values
        
        # 获取目标列索引
        target_idx = available_features.index(target_col)
        
        # 数据标准化 - 使用MinMaxScaler以保持数据的相对关系
        features_scaled = self.feature_scaler.fit_transform(features)
        
        # 分割数据
        n_samples = len(features_scaled)
        train_end = int(n_samples * train_ratio)
        val_end = int(n_samples * (train_ratio + val_ratio))
        
        # 创建数据集
        train_dataset = WindPowerDataset(
            features_scaled[:train_end], self.seq_len, self.pred_len, target_idx
        )
        
        val_dataset = WindPowerDataset(
            features_scaled[train_end:val_end], self.seq_len, self.pred_len, target_idx
        )
        
        test_dataset = WindPowerDataset(
            features_scaled[val_end:], self.seq_len, self.pred_len, target_idx
        )
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, 
                                shuffle=True, drop_last=True, num_workers=0)
        val_loader = DataLoader(val_dataset, batch_size=self.batch_size, 
                              shuffle=False, drop_last=True, num_workers=0)
        test_loader = DataLoader(test_dataset, batch_size=self.batch_size, 
                               shuffle=False, drop_last=False, num_workers=0)
        
        return train_loader, val_loader, test_loader, len(available_features)
    
    def build_model(self, input_dim: int):
        """构建模型"""
        self.model = WindPowerTransformer(
            input_dim=input_dim,
            **self.model_params
        ).to(self.device)
        
        self.optimizer = optim.AdamW(self.model.parameters(), lr=self.learning_rate, 
                                   weight_decay=1e-5, betas=(0.9, 0.999))
        
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=10, T_mult=2, eta_min=1e-6
        )
        
        # 打印模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"总参数数量: {total_params:,}")
        print(f"可训练参数数量: {trainable_params:,}")
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch in train_loader:
            seq_x = batch['seq_x'].to(self.device)
            seq_y = batch['seq_y'].to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            outputs = self.model(seq_x)
            
            # 计算损失
            loss = self.criterion(outputs.squeeze(-1), seq_y)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        self.scheduler.step()
        return total_loss / num_batches
    
    def validate(self, val_loader):
        """验证模型"""
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in val_loader:
                seq_x = batch['seq_x'].to(self.device)
                seq_y = batch['seq_y'].to(self.device)
                
                # 前向传播
                outputs = self.model(seq_x)
                
                # 计算损失
                loss = self.criterion(outputs.squeeze(-1), seq_y)
                total_loss += loss.item()
                
                # 收集预测和真实值
                all_predictions.extend(outputs.squeeze(-1).cpu().numpy().flatten())
                all_targets.extend(seq_y.cpu().numpy().flatten())
        
        avg_loss = total_loss / len(val_loader)
        r2 = r2_score(all_targets, all_predictions)
        
        return avg_loss, r2
    
    def train(self, train_loader, val_loader, epochs: int = 100, patience: int = 20):
        """训练模型"""
        train_losses = []
        val_losses = []
        val_r2_scores = []
        best_val_r2 = -float('inf')
        patience_counter = 0
        best_model_state = None
        
        print("开始训练...")
        for epoch in range(epochs):
            # 训练
            train_loss = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_r2 = self.validate(val_loader)
            
            train_losses.append(train_loss)
            val_losses.append(val_loss)
            val_r2_scores.append(val_r2)
            
            current_lr = self.optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1}/{epochs} - "
                  f"Train Loss: {train_loss:.6f}, "
                  f"Val Loss: {val_loss:.6f}, "
                  f"Val R²: {val_r2:.4f}, "
                  f"LR: {current_lr:.6f}")
            
            # 早停检查（基于R²分数）
            if val_r2 > best_val_r2:
                best_val_r2 = val_r2
                patience_counter = 0
                best_model_state = self.model.state_dict().copy()
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                print(f"早停在第 {epoch+1} 轮，最佳验证R²: {best_val_r2:.6f}")
                break
        
        # 加载最佳模型
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)
        
        return {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'val_r2_scores': val_r2_scores,
            'best_val_r2': best_val_r2
        }
    
    def predict(self, test_loader):
        """预测"""
        self.model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in test_loader:
                seq_x = batch['seq_x'].to(self.device)
                seq_y = batch['seq_y'].to(self.device)
                
                # 前向传播
                outputs = self.model(seq_x)
                
                # 收集预测和真实值
                all_predictions.extend(outputs.squeeze(-1).cpu().numpy())
                all_targets.extend(seq_y.cpu().numpy())
        
        return np.array(all_predictions), np.array(all_targets)
    
    def evaluate(self, predictions, targets):
        """评估模型性能"""
        pred_flat = predictions.flatten()
        target_flat = targets.flatten()
        
        mse = mean_squared_error(target_flat, pred_flat)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(target_flat, pred_flat)
        r2 = r2_score(target_flat, pred_flat)
        
        # 计算MAPE，避免除零错误
        non_zero_mask = np.abs(target_flat) > 1e-8
        if np.any(non_zero_mask):
            mape = np.mean(np.abs((target_flat[non_zero_mask] - pred_flat[non_zero_mask]) / target_flat[non_zero_mask])) * 100
        else:
            mape = float('inf')
        
        return {
            'MSE': mse,
            'RMSE': rmse,
            'MAE': mae,
            'R²': r2,
            'MAPE': mape
        }

def plot_results(history, predictions, targets, metrics):
    """绘制结果"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # 训练历史
    axes[0, 0].plot(history['train_losses'], label='训练损失', alpha=0.8)
    axes[0, 0].plot(history['val_losses'], label='验证损失', alpha=0.8)
    axes[0, 0].set_title('训练历史')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # R²分数
    axes[0, 1].plot(history['val_r2_scores'], label='验证R²', color='green', alpha=0.8)
    axes[0, 1].set_title('验证R²分数')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('R² Score')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 预测样本
    sample_idx = np.random.choice(len(predictions), 1)[0]
    axes[0, 2].plot(targets[sample_idx], label='真实值', alpha=0.8, marker='o')
    axes[0, 2].plot(predictions[sample_idx], label='预测值', alpha=0.8, marker='s')
    axes[0, 2].set_title(f'预测样本 {sample_idx+1}')
    axes[0, 2].set_xlabel('时间步')
    axes[0, 2].set_ylabel('功率 (标准化)')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)

    # 散点图
    pred_flat = predictions.flatten()
    target_flat = targets.flatten()
    axes[1, 0].scatter(target_flat, pred_flat, alpha=0.6, s=10)
    axes[1, 0].plot([target_flat.min(), target_flat.max()],
                   [target_flat.min(), target_flat.max()], 'r--', lw=2)
    axes[1, 0].set_xlabel('真实值')
    axes[1, 0].set_ylabel('预测值')
    axes[1, 0].set_title(f'预测 vs 真实 (R² = {metrics["R²"]:.4f})')
    axes[1, 0].grid(True, alpha=0.3)

    # 误差分布
    errors = pred_flat - target_flat
    axes[1, 1].hist(errors, bins=50, alpha=0.7, edgecolor='black')
    axes[1, 1].set_title('预测误差分布')
    axes[1, 1].set_xlabel('误差')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].grid(True, alpha=0.3)

    # 时间步误差
    step_errors = []
    for step in range(predictions.shape[1]):
        step_pred = predictions[:, step]
        step_target = targets[:, step]
        step_mse = np.mean((step_pred - step_target) ** 2)
        step_errors.append(step_mse)

    axes[1, 2].plot(range(1, len(step_errors) + 1), step_errors, 'o-', linewidth=2, markersize=6)
    axes[1, 2].set_title('不同预测步长的MSE')
    axes[1, 2].set_xlabel('预测步长')
    axes[1, 2].set_ylabel('MSE')
    axes[1, 2].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('final_model_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("=" * 60)
    print("最终优化的风机功率预测模型")
    print("=" * 60)

    # 加载数据
    print("加载数据...")
    df = pd.read_csv('preprocessed_wind_data.csv', index_col=0, parse_dates=True)
    print(f"数据形状: {df.shape}")

    # 模型配置
    config = {
        'seq_len': 48,      # 8小时历史数据
        'pred_len': 12,     # 预测2小时
        'd_model': 128,
        'n_heads': 4,
        'n_layers': 3,
        'dropout': 0.1,
        'learning_rate': 0.001,
        'batch_size': 64
    }

    print("\n模型配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")

    # 创建预测器
    predictor = WindPowerPredictor(**config)

    # 准备数据
    print("\n准备数据...")
    train_loader, val_loader, test_loader, input_dim = predictor.prepare_data(df)

    print(f"训练集批次数: {len(train_loader)}")
    print(f"验证集批次数: {len(val_loader)}")
    print(f"测试集批次数: {len(test_loader)}")
    print(f"输入特征维度: {input_dim}")

    # 构建模型
    print("\n构建模型...")
    predictor.build_model(input_dim)

    # 训练模型
    print("\n开始训练...")
    history = predictor.train(train_loader, val_loader, epochs=100, patience=20)

    # 在测试集上评估
    print("\n在测试集上评估...")
    predictions, targets = predictor.predict(test_loader)
    metrics = predictor.evaluate(predictions, targets)

    print("\n测试集评估结果:")
    print("=" * 40)
    for metric, value in metrics.items():
        print(f"{metric}: {value:.6f}")

    # 检查是否达到精度要求
    r2_score = metrics['R²']
    if r2_score >= 0.9:
        print(f"\n✅ 模型达到精度要求! R² = {r2_score:.4f} >= 0.9")
    else:
        print(f"\n❌ 模型未达到精度要求. R² = {r2_score:.4f} < 0.9")
        if r2_score > 0.8:
            print("模型性能接近要求，可以考虑:")
            print("1. 增加训练轮数")
            print("2. 调整学习率")
            print("3. 增加模型复杂度")
        elif r2_score > 0.6:
            print("模型性能中等，建议:")
            print("1. 增加更多特征")
            print("2. 调整序列长度")
            print("3. 尝试集成学习")
        else:
            print("模型性能较差，需要:")
            print("1. 重新检查数据质量")
            print("2. 尝试不同的模型架构")
            print("3. 增加数据预处理步骤")

    # 绘制结果
    print("\n绘制结果...")
    plot_results(history, predictions, targets, metrics)

    # 保存模型
    print("\n保存模型...")
    torch.save({
        'model_state_dict': predictor.model.state_dict(),
        'config': config,
        'metrics': metrics,
        'feature_scaler': predictor.feature_scaler,
        'history': history
    }, 'final_wind_power_model.pth')

    print("模型已保存到 'final_wind_power_model.pth'")

    # 详细分析
    print(f"\n详细分析:")
    print(f"最佳验证R²: {history['best_val_r2']:.4f}")
    print(f"测试集R²: {r2_score:.4f}")
    print(f"RMSE: {metrics['RMSE']:.4f}")
    print(f"MAE: {metrics['MAE']:.4f}")

    if metrics['MAPE'] != float('inf'):
        print(f"MAPE: {metrics['MAPE']:.2f}%")

    return predictor, metrics

if __name__ == "__main__":
    predictor, metrics = main()
