import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from typing import Optional

class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model: int, max_len: int = 5000, dropout: float = 0.1):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        pe = self.pe[:x.size(0), :].to(x.device)
        x = x + pe
        return self.dropout(x)

class MultiHeadAttention(nn.Module):
    """多头注意力机制"""
    def __init__(self, d_model: int, n_heads: int, dropout: float = 0.1):
        super().__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor, 
                mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        batch_size = query.size(0)
        
        # 线性变换并重塑为多头
        Q = self.w_q(query).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        context = torch.matmul(attn_weights, V)
        
        # 重塑并输出
        context = context.transpose(1, 2).contiguous().view(
            batch_size, -1, self.d_model)
        
        return self.w_o(context)

class FeedForward(nn.Module):
    """前馈网络"""
    def __init__(self, d_model: int, d_ff: int, dropout: float = 0.1):
        super().__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.linear2(self.dropout(F.gelu(self.linear1(x))))

class TransformerBlock(nn.Module):
    """Transformer块"""
    def __init__(self, d_model: int, n_heads: int, d_ff: int, dropout: float = 0.1):
        super().__init__()
        self.attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        # 自注意力
        attn_output = self.attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # 前馈网络
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x

class InputEmbedding(nn.Module):
    """输入嵌入层"""
    def __init__(self, input_dim: int, d_model: int, time_feature_dim: int):
        super().__init__()
        self.input_projection = nn.Linear(input_dim, d_model)
        self.time_projection = nn.Linear(time_feature_dim, d_model)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x: torch.Tensor, time_features: torch.Tensor) -> torch.Tensor:
        # 投影输入特征和时间特征
        x_proj = self.input_projection(x)
        time_proj = self.time_projection(time_features)
        
        # 组合特征
        combined = x_proj + time_proj
        return self.layer_norm(combined)

class TimeSeriesTransformer(nn.Module):
    """时间序列Transformer模型"""
    def __init__(self, input_dim: int, time_feature_dim: int, d_model: int = 512, 
                 n_heads: int = 8, n_layers: int = 6, d_ff: int = 2048, 
                 seq_len: int = 96, pred_len: int = 24, dropout: float = 0.1):
        super().__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # 嵌入层
        self.input_embedding = InputEmbedding(input_dim, d_model, time_feature_dim)
        self.pos_encoding = PositionalEncoding(d_model, max_len=seq_len + pred_len, dropout=dropout)
        
        # Transformer层
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(d_model, n_heads, d_ff, dropout)
            for _ in range(n_layers)
        ])
        
        # 输出层
        self.output_projection = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, pred_len),
            nn.Dropout(dropout)
        )
        
    def forward(self, x: torch.Tensor, time_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        Args:
            x: [batch_size, seq_len, input_dim]
            time_features: [batch_size, seq_len, time_feature_dim]
        Returns:
            predictions: [batch_size, pred_len]
        """
        # 输入嵌入
        embedded = self.input_embedding(x, time_features)
        
        # 位置编码
        embedded = self.pos_encoding(embedded)
        
        # Transformer层
        for transformer_block in self.transformer_blocks:
            embedded = transformer_block(embedded)
        
        # 全局平均池化
        pooled = embedded.mean(dim=1)  # [batch_size, d_model]
        
        # 输出投影
        predictions = self.output_projection(pooled)  # [batch_size, pred_len]
        
        return predictions.unsqueeze(-1)  # [batch_size, pred_len, 1]

class AdvancedTimeSeriesTransformer(nn.Module):
    """高级时间序列Transformer，包含编码器-解码器架构"""
    def __init__(self, input_dim: int, time_feature_dim: int, d_model: int = 512, 
                 n_heads: int = 8, n_encoder_layers: int = 6, n_decoder_layers: int = 3,
                 d_ff: int = 2048, seq_len: int = 96, pred_len: int = 24, 
                 dropout: float = 0.1):
        super().__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # 嵌入层
        self.input_embedding = InputEmbedding(input_dim, d_model, time_feature_dim)
        self.pos_encoding = PositionalEncoding(d_model, max_len=seq_len + pred_len, dropout=dropout)
        
        # 编码器
        self.encoder_blocks = nn.ModuleList([
            TransformerBlock(d_model, n_heads, d_ff, dropout)
            for _ in range(n_encoder_layers)
        ])
        
        # 解码器
        self.decoder_blocks = nn.ModuleList([
            TransformerBlock(d_model, n_heads, d_ff, dropout)
            for _ in range(n_decoder_layers)
        ])
        
        # 查询生成器（用于解码器）
        self.query_generator = nn.Parameter(torch.randn(pred_len, d_model))
        
        # 输出层
        self.output_projection = nn.Linear(d_model, 1)
        
    def forward(self, x: torch.Tensor, time_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        Args:
            x: [batch_size, seq_len, input_dim]
            time_features: [batch_size, seq_len, time_feature_dim]
        Returns:
            predictions: [batch_size, pred_len, 1]
        """
        batch_size = x.size(0)
        
        # 编码器输入嵌入
        encoder_input = self.input_embedding(x, time_features)
        encoder_input = self.pos_encoding(encoder_input)
        
        # 编码器
        encoder_output = encoder_input
        for encoder_block in self.encoder_blocks:
            encoder_output = encoder_block(encoder_output)
        
        # 解码器查询
        decoder_queries = self.query_generator.unsqueeze(0).expand(batch_size, -1, -1)
        decoder_queries = self.pos_encoding(decoder_queries)
        
        # 解码器（使用交叉注意力）
        decoder_output = decoder_queries
        for decoder_block in self.decoder_blocks:
            # 自注意力
            decoder_output = decoder_block(decoder_output)
            
            # 交叉注意力（简化版本：直接加权组合）
            attn_weights = F.softmax(
                torch.matmul(decoder_output, encoder_output.transpose(-2, -1)) / math.sqrt(self.d_model),
                dim=-1
            )
            cross_attn = torch.matmul(attn_weights, encoder_output)
            decoder_output = decoder_output + cross_attn
        
        # 输出投影
        predictions = self.output_projection(decoder_output)
        
        return predictions
