import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class OptimizedTimeSeriesDataset(Dataset):
    """优化的时间序列数据集"""
    def __init__(self, data: np.ndarray, seq_len: int = 96, pred_len: int = 24, target_col: int = 1):
        self.data = data
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.target_col = target_col
        
    def __len__(self):
        return len(self.data) - self.seq_len - self.pred_len + 1
    
    def __getitem__(self, idx):
        # 输入序列
        seq_x = self.data[idx:idx + self.seq_len]
        seq_y = self.data[idx + self.seq_len:idx + self.seq_len + self.pred_len, self.target_col]
        
        return {
            'seq_x': torch.FloatTensor(seq_x),
            'seq_y': torch.FloatTensor(seq_y)
        }

class LSTMTransformerHybrid(nn.Module):
    """LSTM-Transformer混合模型"""
    def __init__(self, input_dim: int, d_model: int = 256, n_heads: int = 8, 
                 n_layers: int = 4, lstm_hidden: int = 128, seq_len: int = 96, 
                 pred_len: int = 24, dropout: float = 0.1):
        super().__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # LSTM层用于捕获时间依赖
        self.lstm = nn.LSTM(input_dim, lstm_hidden, batch_first=True, 
                           num_layers=2, dropout=dropout, bidirectional=True)
        
        # 投影层
        self.input_projection = nn.Linear(lstm_hidden * 2, d_model)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 注意力池化
        self.attention_pool = nn.MultiheadAttention(d_model, n_heads, dropout=dropout, batch_first=True)
        self.pool_query = nn.Parameter(torch.randn(1, d_model))
        
        # 输出层
        self.output_layers = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, pred_len),
            nn.Dropout(dropout)
        )
        
        # 残差连接
        self.residual_projection = nn.Linear(input_dim, pred_len)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        batch_size = x.size(0)
        
        # LSTM特征提取
        lstm_out, _ = self.lstm(x)
        
        # 投影到Transformer维度
        transformer_input = self.input_projection(lstm_out)
        
        # Transformer编码
        transformer_out = self.transformer(transformer_input)
        
        # 注意力池化
        query = self.pool_query.unsqueeze(0).expand(batch_size, -1, -1)
        pooled, _ = self.attention_pool(query, transformer_out, transformer_out)
        pooled = pooled.squeeze(1)
        
        # 主要输出
        main_output = self.output_layers(pooled)
        
        # 残差连接（使用最后一个时间步）
        residual = self.residual_projection(x[:, -1, :])
        
        # 组合输出
        output = main_output + 0.1 * residual
        
        return output.unsqueeze(-1)

class CNNTransformer(nn.Module):
    """CNN-Transformer混合模型"""
    def __init__(self, input_dim: int, d_model: int = 256, n_heads: int = 8, 
                 n_layers: int = 4, seq_len: int = 96, pred_len: int = 24, 
                 dropout: float = 0.1):
        super().__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        
        # CNN特征提取
        self.conv_layers = nn.Sequential(
            nn.Conv1d(input_dim, 64, kernel_size=3, padding=1),
            nn.BatchNorm1d(64),
            nn.GELU(),
            nn.Conv1d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm1d(128),
            nn.GELU(),
            nn.Conv1d(128, d_model, kernel_size=3, padding=1),
            nn.BatchNorm1d(d_model),
            nn.GELU(),
        )
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(seq_len, d_model))
        
        # Transformer
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 输出层
        self.output_layers = nn.Sequential(
            nn.Linear(d_model, d_model * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 2, d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, pred_len)
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # CNN特征提取
        x_conv = x.transpose(1, 2)  # [B, C, L]
        conv_out = self.conv_layers(x_conv)
        conv_out = conv_out.transpose(1, 2)  # [B, L, C]
        
        # 位置编码
        conv_out = conv_out + self.pos_encoding.unsqueeze(0)
        
        # Transformer
        transformer_out = self.transformer(conv_out)
        
        # 全局池化
        pooled = self.global_pool(transformer_out.transpose(1, 2)).squeeze(-1)
        
        # 输出
        output = self.output_layers(pooled)
        
        return output.unsqueeze(-1)

class OptimizedPredictor:
    """优化的预测器"""
    def __init__(self, input_dim: int, model_type: str = 'lstm_transformer',
                 seq_len: int = 96, pred_len: int = 24, d_model: int = 256, 
                 n_heads: int = 8, n_layers: int = 4, dropout: float = 0.1,
                 learning_rate: float = 0.001, batch_size: int = 32):
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        
        # 设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 模型
        if model_type == 'lstm_transformer':
            self.model = LSTMTransformerHybrid(
                input_dim=input_dim,
                d_model=d_model,
                n_heads=n_heads,
                n_layers=n_layers,
                seq_len=seq_len,
                pred_len=pred_len,
                dropout=dropout
            ).to(self.device)
        else:
            self.model = CNNTransformer(
                input_dim=input_dim,
                d_model=d_model,
                n_heads=n_heads,
                n_layers=n_layers,
                seq_len=seq_len,
                pred_len=pred_len,
                dropout=dropout
            ).to(self.device)
        
        # 优化器和调度器
        self.optimizer = optim.AdamW(self.model.parameters(), lr=learning_rate, 
                                   weight_decay=1e-4, betas=(0.9, 0.95))
        self.scheduler = optim.lr_scheduler.OneCycleLR(
            self.optimizer, max_lr=learning_rate, 
            steps_per_epoch=100, epochs=100,  # 这些会在训练时更新
            pct_start=0.1, anneal_strategy='cos'
        )
        
        # 损失函数
        self.criterion = nn.SmoothL1Loss()  # 使用Huber损失，对异常值更鲁棒
        
        # 数据预处理器
        self.feature_scaler = StandardScaler()
        
        # 打印模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"总参数数量: {total_params:,}")
        print(f"可训练参数数量: {trainable_params:,}")
    
    def prepare_data(self, df: pd.DataFrame, target_col: str = 'power', 
                     train_ratio: float = 0.7, val_ratio: float = 0.2):
        """准备数据"""
        # 选择特征列
        feature_cols = ['wind_speed', 'power', 'rotor_speed', 'blade_angle']
        
        # 确保所有需要的列都存在
        available_features = [col for col in feature_cols if col in df.columns]
        print(f"可用特征: {available_features}")
        
        # 提取特征
        features = df[available_features].values
        
        # 获取目标列索引
        target_idx = available_features.index(target_col)
        
        # 数据标准化
        features_scaled = self.feature_scaler.fit_transform(features)
        
        # 分割数据
        n_samples = len(features_scaled)
        train_end = int(n_samples * train_ratio)
        val_end = int(n_samples * (train_ratio + val_ratio))
        
        # 创建数据集
        train_dataset = OptimizedTimeSeriesDataset(
            features_scaled[:train_end], self.seq_len, self.pred_len, target_idx
        )
        
        val_dataset = OptimizedTimeSeriesDataset(
            features_scaled[train_end:val_end], self.seq_len, self.pred_len, target_idx
        )
        
        test_dataset = OptimizedTimeSeriesDataset(
            features_scaled[val_end:], self.seq_len, self.pred_len, target_idx
        )
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, 
                                shuffle=True, drop_last=True, num_workers=0)
        val_loader = DataLoader(val_dataset, batch_size=self.batch_size, 
                              shuffle=False, drop_last=True, num_workers=0)
        test_loader = DataLoader(test_dataset, batch_size=self.batch_size, 
                               shuffle=False, drop_last=False, num_workers=0)
        
        # 更新调度器
        self.scheduler = optim.lr_scheduler.OneCycleLR(
            self.optimizer, max_lr=self.learning_rate, 
            steps_per_epoch=len(train_loader), epochs=100,
            pct_start=0.1, anneal_strategy='cos'
        )
        
        return train_loader, val_loader, test_loader
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch in train_loader:
            seq_x = batch['seq_x'].to(self.device)
            seq_y = batch['seq_y'].to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            outputs = self.model(seq_x)
            
            # 计算损失
            loss = self.criterion(outputs.squeeze(-1), seq_y)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            self.scheduler.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        return total_loss / num_batches
    
    def validate(self, val_loader):
        """验证模型"""
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in val_loader:
                seq_x = batch['seq_x'].to(self.device)
                seq_y = batch['seq_y'].to(self.device)
                
                # 前向传播
                outputs = self.model(seq_x)
                
                # 计算损失
                loss = self.criterion(outputs.squeeze(-1), seq_y)
                total_loss += loss.item()
                
                # 收集预测和真实值
                all_predictions.extend(outputs.squeeze(-1).cpu().numpy().flatten())
                all_targets.extend(seq_y.cpu().numpy().flatten())
        
        avg_loss = total_loss / len(val_loader)
        r2 = r2_score(all_targets, all_predictions)

        return avg_loss, r2

    def train(self, train_loader, val_loader, epochs: int = 100, patience: int = 15):
        """训练模型"""
        train_losses = []
        val_losses = []
        val_r2_scores = []
        best_val_loss = float('inf')
        patience_counter = 0
        best_model_state = None

        print("开始训练...")
        for epoch in range(epochs):
            # 训练
            train_loss = self.train_epoch(train_loader)

            # 验证
            val_loss, val_r2 = self.validate(val_loader)

            train_losses.append(train_loss)
            val_losses.append(val_loss)
            val_r2_scores.append(val_r2)

            current_lr = self.optimizer.param_groups[0]['lr']
            print(f"Epoch {epoch+1}/{epochs} - "
                  f"Train Loss: {train_loss:.6f}, "
                  f"Val Loss: {val_loss:.6f}, "
                  f"Val R²: {val_r2:.4f}, "
                  f"LR: {current_lr:.6f}")

            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                best_model_state = self.model.state_dict().copy()
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f"早停在第 {epoch+1} 轮，最佳验证损失: {best_val_loss:.6f}")
                break

        # 加载最佳模型
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)

        return {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'val_r2_scores': val_r2_scores,
            'best_val_loss': best_val_loss
        }

    def predict(self, test_loader):
        """预测"""
        self.model.eval()
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch in test_loader:
                seq_x = batch['seq_x'].to(self.device)
                seq_y = batch['seq_y'].to(self.device)

                # 前向传播
                outputs = self.model(seq_x)

                # 收集预测和真实值
                all_predictions.extend(outputs.squeeze(-1).cpu().numpy())
                all_targets.extend(seq_y.cpu().numpy())

        return np.array(all_predictions), np.array(all_targets)

    def evaluate(self, predictions, targets):
        """评估模型性能"""
        pred_flat = predictions.flatten()
        target_flat = targets.flatten()

        mse = mean_squared_error(target_flat, pred_flat)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(target_flat, pred_flat)
        r2 = r2_score(target_flat, pred_flat)

        # 避免除零错误
        non_zero_mask = np.abs(target_flat) > 1e-8
        if np.any(non_zero_mask):
            mape = np.mean(np.abs((target_flat[non_zero_mask] - pred_flat[non_zero_mask]) / target_flat[non_zero_mask])) * 100
        else:
            mape = float('inf')

        return {
            'MSE': mse,
            'RMSE': rmse,
            'MAE': mae,
            'R²': r2,
            'MAPE': mape
        }

def main():
    """主函数"""
    print("=" * 60)
    print("优化Transformer时间序列预测模型")
    print("=" * 60)

    # 加载数据
    print("加载数据...")
    df = pd.read_csv('preprocessed_wind_data.csv', index_col=0, parse_dates=True)
    print(f"数据形状: {df.shape}")

    # 测试两种模型
    model_configs = [
        {
            'name': 'LSTM-Transformer',
            'type': 'lstm_transformer',
            'config': {
                'seq_len': 96,
                'pred_len': 24,
                'd_model': 256,
                'n_heads': 8,
                'n_layers': 4,
                'dropout': 0.1,
                'learning_rate': 0.001,
                'batch_size': 32
            }
        },
        {
            'name': 'CNN-Transformer',
            'type': 'cnn_transformer',
            'config': {
                'seq_len': 96,
                'pred_len': 24,
                'd_model': 256,
                'n_heads': 8,
                'n_layers': 4,
                'dropout': 0.1,
                'learning_rate': 0.001,
                'batch_size': 32
            }
        }
    ]

    best_model = None
    best_metrics = None
    best_r2 = -float('inf')

    for model_config in model_configs:
        print(f"\n{'='*60}")
        print(f"训练 {model_config['name']} 模型")
        print(f"{'='*60}")

        # 创建预测器
        predictor = OptimizedPredictor(
            input_dim=4,  # wind_speed, power, rotor_speed, blade_angle
            model_type=model_config['type'],
            **model_config['config']
        )

        # 准备数据
        print("\n准备数据...")
        train_loader, val_loader, test_loader = predictor.prepare_data(df)

        print(f"训练集批次数: {len(train_loader)}")
        print(f"验证集批次数: {len(val_loader)}")
        print(f"测试集批次数: {len(test_loader)}")

        # 训练模型
        print("\n开始训练...")
        history = predictor.train(train_loader, val_loader, epochs=50, patience=10)

        # 在测试集上评估
        print("\n在测试集上评估...")
        predictions, targets = predictor.predict(test_loader)
        metrics = predictor.evaluate(predictions, targets)

        print(f"\n{model_config['name']} 测试集评估结果:")
        print("=" * 40)
        for metric, value in metrics.items():
            print(f"{metric}: {value:.6f}")

        # 检查是否是最佳模型
        if metrics['R²'] > best_r2:
            best_r2 = metrics['R²']
            best_model = predictor
            best_metrics = metrics
            best_model_name = model_config['name']

            # 保存最佳模型
            torch.save({
                'model_state_dict': predictor.model.state_dict(),
                'config': model_config,
                'metrics': metrics,
                'feature_scaler': predictor.feature_scaler
            }, f'best_model_{model_config["type"]}.pth')

    print(f"\n{'='*60}")
    print("最终结果")
    print(f"{'='*60}")
    print(f"最佳模型: {best_model_name}")
    print(f"最佳R²分数: {best_r2:.4f}")

    if best_r2 >= 0.9:
        print(f"\n✅ 模型达到精度要求! R² = {best_r2:.4f} >= 0.9")
    else:
        print(f"\n❌ 模型未达到精度要求. R² = {best_r2:.4f} < 0.9")
        print("建议:")
        print("1. 增加更多特征工程")
        print("2. 尝试集成学习方法")
        print("3. 调整序列长度和预测长度")
        print("4. 使用更复杂的模型架构")

    return best_model, best_metrics

if __name__ == "__main__":
    best_model, best_metrics = main()
