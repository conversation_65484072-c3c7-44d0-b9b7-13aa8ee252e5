import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tuple, List, Optional
import warnings
warnings.filterwarnings('ignore')

from advanced_transformer import AdvancedTransformerPredictor

class TimeSeriesDataset(Dataset):
    """
    时间序列数据集类
    """
    def __init__(self, data: np.ndarray, time_features: np.ndarray, 
                 seq_len: int = 96, pred_len: int = 24, target_col: int = 0):
        self.data = data
        self.time_features = time_features
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.target_col = target_col
        
    def __len__(self):
        return len(self.data) - self.seq_len - self.pred_len + 1
    
    def __getitem__(self, idx):
        # 输入序列
        seq_x = self.data[idx:idx + self.seq_len]
        seq_y = self.data[idx + self.seq_len:idx + self.seq_len + self.pred_len, self.target_col]
        
        # 时间特征
        seq_x_mark = self.time_features[idx:idx + self.seq_len]
        seq_y_mark = self.time_features[idx + self.seq_len:idx + self.seq_len + self.pred_len]
        
        # 合并时间特征用于解码器
        seq_mark = np.concatenate([seq_x_mark, seq_y_mark], axis=0)
        
        return {
            'seq_x': torch.FloatTensor(seq_x),
            'seq_y': torch.FloatTensor(seq_y),
            'seq_x_mark': torch.FloatTensor(seq_x_mark),
            'seq_mark': torch.FloatTensor(seq_mark)
        }

class TimeSeriesPredictor:
    """
    时间序列预测器主类
    """
    def __init__(self, seq_len: int = 96, pred_len: int = 24, d_model: int = 512, 
                 n_heads: int = 8, e_layers: int = 2, d_layers: int = 1, 
                 d_ff: int = 2048, factor: int = 5, dropout: float = 0.05,
                 learning_rate: float = 0.0001, batch_size: int = 32):
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        
        # 数据预处理器
        self.feature_scaler = StandardScaler()
        self.target_scaler = StandardScaler()
        
        # 设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 模型参数
        self.model_params = {
            'seq_len': seq_len,
            'pred_len': pred_len,
            'd_model': d_model,
            'n_heads': n_heads,
            'e_layers': e_layers,
            'd_layers': d_layers,
            'd_ff': d_ff,
            'factor': factor,
            'dropout': dropout
        }
        
        self.model = None
        self.optimizer = None
        self.criterion = nn.MSELoss()
        
    def prepare_data(self, df: pd.DataFrame, target_col: str = 'power', 
                     train_ratio: float = 0.7, val_ratio: float = 0.2) -> Tuple:
        """
        准备训练数据
        """
        # 选择特征列
        feature_cols = ['wind_speed', 'power', 'rotor_speed', 'blade_angle']
        time_feature_cols = ['hour_sin', 'hour_cos', 'day_sin', 'day_cos', 
                           'month_sin', 'month_cos', 'hour', 'dayofweek']
        
        # 确保所有需要的列都存在
        available_features = [col for col in feature_cols if col in df.columns]
        available_time_features = [col for col in time_feature_cols if col in df.columns]
        
        print(f"可用特征: {available_features}")
        print(f"可用时间特征: {available_time_features}")
        
        # 提取特征和目标
        features = df[available_features].values
        time_features = df[available_time_features].values
        
        # 获取目标列索引
        target_idx = available_features.index(target_col)
        
        # 数据标准化
        features_scaled = self.feature_scaler.fit_transform(features)
        
        # 分割数据
        n_samples = len(features_scaled)
        train_end = int(n_samples * train_ratio)
        val_end = int(n_samples * (train_ratio + val_ratio))
        
        # 训练集
        train_features = features_scaled[:train_end]
        train_time_features = time_features[:train_end]
        
        # 验证集
        val_features = features_scaled[train_end:val_end]
        val_time_features = time_features[train_end:val_end]
        
        # 测试集
        test_features = features_scaled[val_end:]
        test_time_features = time_features[val_end:]
        
        # 创建数据集
        train_dataset = TimeSeriesDataset(
            train_features, train_time_features, 
            self.seq_len, self.pred_len, target_idx
        )
        
        val_dataset = TimeSeriesDataset(
            val_features, val_time_features, 
            self.seq_len, self.pred_len, target_idx
        )
        
        test_dataset = TimeSeriesDataset(
            test_features, test_time_features, 
            self.seq_len, self.pred_len, target_idx
        )
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, 
                                shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=self.batch_size, 
                              shuffle=False, drop_last=True)
        test_loader = DataLoader(test_dataset, batch_size=self.batch_size, 
                               shuffle=False, drop_last=False)
        
        return train_loader, val_loader, test_loader, len(available_features), len(available_time_features)
    
    def build_model(self, input_dim: int, time_feature_dim: int):
        """
        构建模型
        """
        self.model = AdvancedTransformerPredictor(
            input_dim=input_dim,
            output_dim=1,
            time_feature_dim=time_feature_dim,
            **self.model_params
        ).to(self.device)
        
        self.optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
        
        # 打印模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"总参数数量: {total_params:,}")
        print(f"可训练参数数量: {trainable_params:,}")
    
    def train_epoch(self, train_loader: DataLoader) -> float:
        """
        训练一个epoch
        """
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch in train_loader:
            seq_x = batch['seq_x'].to(self.device)
            seq_y = batch['seq_y'].to(self.device)
            seq_x_mark = batch['seq_x_mark'].to(self.device)
            seq_mark = batch['seq_mark'].to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            outputs = self.model(seq_x, seq_mark)
            
            # 计算损失
            loss = self.criterion(outputs.squeeze(-1), seq_y)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        return total_loss / num_batches
    
    def validate(self, val_loader: DataLoader) -> Tuple[float, float]:
        """
        验证模型
        """
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in val_loader:
                seq_x = batch['seq_x'].to(self.device)
                seq_y = batch['seq_y'].to(self.device)
                seq_x_mark = batch['seq_x_mark'].to(self.device)
                seq_mark = batch['seq_mark'].to(self.device)
                
                # 前向传播
                outputs = self.model(seq_x, seq_mark)
                
                # 计算损失
                loss = self.criterion(outputs.squeeze(-1), seq_y)
                total_loss += loss.item()
                
                # 收集预测和真实值
                all_predictions.extend(outputs.squeeze(-1).cpu().numpy().flatten())
                all_targets.extend(seq_y.cpu().numpy().flatten())
        
        avg_loss = total_loss / len(val_loader)
        
        # 计算R²分数
        r2 = r2_score(all_targets, all_predictions)

        return avg_loss, r2

    def train(self, train_loader: DataLoader, val_loader: DataLoader,
              epochs: int = 100, patience: int = 10, min_delta: float = 1e-4) -> dict:
        """
        训练模型
        """
        train_losses = []
        val_losses = []
        val_r2_scores = []
        best_val_loss = float('inf')
        patience_counter = 0
        best_model_state = None

        print("开始训练...")
        for epoch in range(epochs):
            # 训练
            train_loss = self.train_epoch(train_loader)

            # 验证
            val_loss, val_r2 = self.validate(val_loader)

            train_losses.append(train_loss)
            val_losses.append(val_loss)
            val_r2_scores.append(val_r2)

            print(f"Epoch {epoch+1}/{epochs} - "
                  f"Train Loss: {train_loss:.6f}, "
                  f"Val Loss: {val_loss:.6f}, "
                  f"Val R²: {val_r2:.4f}")

            # 早停检查
            if val_loss < best_val_loss - min_delta:
                best_val_loss = val_loss
                patience_counter = 0
                best_model_state = self.model.state_dict().copy()
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f"早停在第 {epoch+1} 轮，最佳验证损失: {best_val_loss:.6f}")
                break

        # 加载最佳模型
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)

        return {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'val_r2_scores': val_r2_scores,
            'best_val_loss': best_val_loss
        }

    def predict(self, test_loader: DataLoader) -> Tuple[np.ndarray, np.ndarray]:
        """
        预测
        """
        self.model.eval()
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch in test_loader:
                seq_x = batch['seq_x'].to(self.device)
                seq_y = batch['seq_y'].to(self.device)
                seq_x_mark = batch['seq_x_mark'].to(self.device)
                seq_mark = batch['seq_mark'].to(self.device)

                # 前向传播
                outputs = self.model(seq_x, seq_mark)

                # 收集预测和真实值
                all_predictions.extend(outputs.squeeze(-1).cpu().numpy())
                all_targets.extend(seq_y.cpu().numpy())

        return np.array(all_predictions), np.array(all_targets)

    def evaluate(self, predictions: np.ndarray, targets: np.ndarray) -> dict:
        """
        评估模型性能
        """
        # 展平数组
        pred_flat = predictions.flatten()
        target_flat = targets.flatten()

        # 计算各种指标
        mse = mean_squared_error(target_flat, pred_flat)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(target_flat, pred_flat)
        r2 = r2_score(target_flat, pred_flat)

        # 计算MAPE
        mape = np.mean(np.abs((target_flat - pred_flat) / target_flat)) * 100

        metrics = {
            'MSE': mse,
            'RMSE': rmse,
            'MAE': mae,
            'R²': r2,
            'MAPE': mape
        }

        return metrics

    def plot_training_history(self, history: dict):
        """
        绘制训练历史
        """
        fig, axes = plt.subplots(1, 3, figsize=(18, 5))

        # 损失曲线
        axes[0].plot(history['train_losses'], label='训练损失', alpha=0.8)
        axes[0].plot(history['val_losses'], label='验证损失', alpha=0.8)
        axes[0].set_title('训练和验证损失')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)

        # R²分数
        axes[1].plot(history['val_r2_scores'], label='验证R²', color='green', alpha=0.8)
        axes[1].set_title('验证R²分数')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('R² Score')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)

        # 学习曲线对比
        axes[2].plot(history['train_losses'], label='训练损失', alpha=0.8)
        axes[2].plot(history['val_losses'], label='验证损失', alpha=0.8)
        axes[2].set_title('学习曲线对比')
        axes[2].set_xlabel('Epoch')
        axes[2].set_ylabel('Loss')
        axes[2].set_yscale('log')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_predictions(self, predictions: np.ndarray, targets: np.ndarray,
                        num_samples: int = 5):
        """
        绘制预测结果
        """
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))

        # 选择几个样本进行可视化
        sample_indices = np.random.choice(len(predictions), num_samples, replace=False)

        for i, idx in enumerate(sample_indices):
            if i >= 6:  # 最多显示6个样本
                break

            row = i // 3
            col = i % 3

            axes[row, col].plot(targets[idx], label='真实值', alpha=0.8)
            axes[row, col].plot(predictions[idx], label='预测值', alpha=0.8)
            axes[row, col].set_title(f'样本 {idx+1}')
            axes[row, col].set_xlabel('时间步')
            axes[row, col].set_ylabel('功率 (标准化)')
            axes[row, col].legend()
            axes[row, col].grid(True, alpha=0.3)

        # 如果样本数少于6个，隐藏多余的子图
        for i in range(num_samples, 6):
            row = i // 3
            col = i % 3
            axes[row, col].set_visible(False)

        plt.tight_layout()
        plt.savefig('prediction_samples.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 散点图：预测值 vs 真实值
        plt.figure(figsize=(10, 8))
        pred_flat = predictions.flatten()
        target_flat = targets.flatten()

        plt.scatter(target_flat, pred_flat, alpha=0.5, s=1)
        plt.plot([target_flat.min(), target_flat.max()],
                [target_flat.min(), target_flat.max()], 'r--', lw=2)
        plt.xlabel('真实值')
        plt.ylabel('预测值')
        plt.title('预测值 vs 真实值')
        plt.grid(True, alpha=0.3)

        # 添加R²分数
        r2 = r2_score(target_flat, pred_flat)
        plt.text(0.05, 0.95, f'R² = {r2:.4f}', transform=plt.gca().transAxes,
                fontsize=12, bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        plt.savefig('prediction_scatter.png', dpi=300, bbox_inches='tight')
        plt.show()
