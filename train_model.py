import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

from time_series_model import TimeSeriesPredictor

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def main():
    """
    主训练函数
    """
    print("=" * 60)
    print("高级Transformer时间序列预测模型训练")
    print("=" * 60)
    
    # 加载预处理后的数据
    print("加载数据...")
    df = pd.read_csv('preprocessed_wind_data.csv', index_col=0, parse_dates=True)
    print(f"数据形状: {df.shape}")
    print(f"数据列: {df.columns.tolist()}")
    
    # 模型超参数
    model_config = {
        'seq_len': 96,          # 输入序列长度 (4天，每10分钟一个点)
        'pred_len': 24,         # 预测长度 (4小时)
        'd_model': 512,         # 模型维度
        'n_heads': 8,           # 注意力头数
        'e_layers': 3,          # 编码器层数
        'd_layers': 2,          # 解码器层数
        'd_ff': 2048,           # 前馈网络维度
        'factor': 5,            # ProbSparse因子
        'dropout': 0.05,        # Dropout率
        'learning_rate': 0.0001, # 学习率
        'batch_size': 32        # 批次大小
    }
    
    print("\n模型配置:")
    for key, value in model_config.items():
        print(f"  {key}: {value}")
    
    # 创建预测器
    predictor = TimeSeriesPredictor(**model_config)
    
    # 准备数据
    print("\n准备数据...")
    train_loader, val_loader, test_loader, input_dim, time_feature_dim = predictor.prepare_data(
        df, target_col='power', train_ratio=0.7, val_ratio=0.2
    )
    
    print(f"训练集批次数: {len(train_loader)}")
    print(f"验证集批次数: {len(val_loader)}")
    print(f"测试集批次数: {len(test_loader)}")
    print(f"输入特征维度: {input_dim}")
    print(f"时间特征维度: {time_feature_dim}")
    
    # 构建模型
    print("\n构建模型...")
    predictor.build_model(input_dim, time_feature_dim)
    
    # 训练模型
    print("\n开始训练...")
    training_config = {
        'epochs': 100,
        'patience': 15,
        'min_delta': 1e-5
    }
    
    history = predictor.train(
        train_loader, val_loader, 
        epochs=training_config['epochs'],
        patience=training_config['patience'],
        min_delta=training_config['min_delta']
    )
    
    # 绘制训练历史
    print("\n绘制训练历史...")
    predictor.plot_training_history(history)
    
    # 在测试集上评估
    print("\n在测试集上评估...")
    predictions, targets = predictor.predict(test_loader)
    
    # 计算评估指标
    metrics = predictor.evaluate(predictions, targets)
    
    print("\n测试集评估结果:")
    print("=" * 40)
    for metric, value in metrics.items():
        print(f"{metric}: {value:.6f}")
    
    # 检查是否达到精度要求
    r2_score = metrics['R²']
    if r2_score >= 0.9:
        print(f"\n✅ 模型达到精度要求! R² = {r2_score:.4f} >= 0.9")
    else:
        print(f"\n❌ 模型未达到精度要求. R² = {r2_score:.4f} < 0.9")
        print("建议:")
        print("1. 增加模型复杂度 (更多层数、更大维度)")
        print("2. 调整学习率")
        print("3. 增加训练轮数")
        print("4. 尝试不同的数据预处理方法")
    
    # 绘制预测结果
    print("\n绘制预测结果...")
    predictor.plot_predictions(predictions, targets, num_samples=6)
    
    # 保存模型
    print("\n保存模型...")
    torch.save({
        'model_state_dict': predictor.model.state_dict(),
        'model_config': model_config,
        'metrics': metrics,
        'feature_scaler': predictor.feature_scaler,
        'target_scaler': predictor.target_scaler
    }, 'wind_power_transformer_model.pth')
    
    print("模型已保存到 'wind_power_transformer_model.pth'")
    
    # 详细分析
    print("\n" + "=" * 60)
    print("详细分析")
    print("=" * 60)
    
    # 计算不同时间步的平均误差
    pred_flat = predictions.flatten()
    target_flat = targets.flatten()
    
    # 按预测步长分析误差
    step_errors = []
    for step in range(predictor.pred_len):
        step_pred = predictions[:, step]
        step_target = targets[:, step]
        step_mse = np.mean((step_pred - step_target) ** 2)
        step_errors.append(step_mse)
    
    # 绘制不同预测步长的误差
    plt.figure(figsize=(12, 6))
    plt.plot(range(1, predictor.pred_len + 1), step_errors, 'o-', linewidth=2, markersize=6)
    plt.title('不同预测步长的MSE误差')
    plt.xlabel('预测步长')
    plt.ylabel('MSE')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('step_wise_errors.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"平均MSE: {np.mean(step_errors):.6f}")
    print(f"最小MSE (步长 {np.argmin(step_errors)+1}): {np.min(step_errors):.6f}")
    print(f"最大MSE (步长 {np.argmax(step_errors)+1}): {np.max(step_errors):.6f}")
    
    return predictor, metrics

def hyperparameter_tuning():
    """
    超参数调优
    """
    print("\n" + "=" * 60)
    print("超参数调优")
    print("=" * 60)
    
    # 加载数据
    df = pd.read_csv('preprocessed_wind_data.csv', index_col=0, parse_dates=True)
    
    # 定义超参数搜索空间
    param_grid = {
        'd_model': [256, 512, 768],
        'n_heads': [4, 8, 16],
        'e_layers': [2, 3, 4],
        'learning_rate': [0.0001, 0.0005, 0.001],
        'dropout': [0.05, 0.1, 0.15]
    }
    
    best_r2 = 0
    best_params = None
    results = []
    
    # 简化的网格搜索（随机采样）
    import random
    
    num_trials = 10  # 限制试验次数
    print(f"进行 {num_trials} 次随机超参数搜索...")
    
    for trial in range(num_trials):
        # 随机选择超参数
        params = {
            'seq_len': 96,
            'pred_len': 24,
            'd_model': random.choice(param_grid['d_model']),
            'n_heads': random.choice(param_grid['n_heads']),
            'e_layers': random.choice(param_grid['e_layers']),
            'd_layers': 1,
            'd_ff': None,  # 将在模型中自动设置为4*d_model
            'factor': 5,
            'dropout': random.choice(param_grid['dropout']),
            'learning_rate': random.choice(param_grid['learning_rate']),
            'batch_size': 32
        }
        
        # 确保d_ff是d_model的4倍
        params['d_ff'] = 4 * params['d_model']
        
        print(f"\n试验 {trial+1}/{num_trials}")
        print(f"参数: {params}")
        
        try:
            # 创建预测器
            predictor = TimeSeriesPredictor(**params)
            
            # 准备数据
            train_loader, val_loader, test_loader, input_dim, time_feature_dim = predictor.prepare_data(
                df, target_col='power', train_ratio=0.7, val_ratio=0.2
            )
            
            # 构建模型
            predictor.build_model(input_dim, time_feature_dim)
            
            # 训练模型（减少训练轮数以节省时间）
            history = predictor.train(train_loader, val_loader, epochs=30, patience=10)
            
            # 评估
            predictions, targets = predictor.predict(test_loader)
            metrics = predictor.evaluate(predictions, targets)
            
            r2_score = metrics['R²']
            results.append({
                'trial': trial + 1,
                'params': params.copy(),
                'r2_score': r2_score,
                'metrics': metrics
            })
            
            print(f"R² 分数: {r2_score:.4f}")
            
            # 更新最佳结果
            if r2_score > best_r2:
                best_r2 = r2_score
                best_params = params.copy()
                print(f"新的最佳结果! R² = {best_r2:.4f}")
            
        except Exception as e:
            print(f"试验失败: {e}")
            continue
    
    print("\n" + "=" * 60)
    print("超参数调优结果")
    print("=" * 60)
    print(f"最佳R²分数: {best_r2:.4f}")
    print(f"最佳参数: {best_params}")
    
    # 保存调优结果
    import json
    with open('hyperparameter_tuning_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    return best_params, results

if __name__ == "__main__":
    # 主训练
    predictor, metrics = main()
    
    # 如果精度不够，进行超参数调优
    if metrics['R²'] < 0.9:
        print("\n精度未达到要求，开始超参数调优...")
        best_params, tuning_results = hyperparameter_tuning()
        
        # 使用最佳参数重新训练
        print("\n使用最佳参数重新训练...")
        df = pd.read_csv('preprocessed_wind_data.csv', index_col=0, parse_dates=True)
        
        final_predictor = TimeSeriesPredictor(**best_params)
        train_loader, val_loader, test_loader, input_dim, time_feature_dim = final_predictor.prepare_data(
            df, target_col='power', train_ratio=0.7, val_ratio=0.2
        )
        final_predictor.build_model(input_dim, time_feature_dim)
        
        final_history = final_predictor.train(train_loader, val_loader, epochs=100, patience=15)
        final_predictions, final_targets = final_predictor.predict(test_loader)
        final_metrics = final_predictor.evaluate(final_predictions, final_targets)
        
        print("\n最终模型评估结果:")
        for metric, value in final_metrics.items():
            print(f"{metric}: {value:.6f}")
        
        # 保存最终模型
        torch.save({
            'model_state_dict': final_predictor.model.state_dict(),
            'model_config': best_params,
            'metrics': final_metrics,
            'feature_scaler': final_predictor.feature_scaler,
            'target_scaler': final_predictor.target_scaler
        }, 'wind_power_transformer_model_optimized.pth')
    
    print("\n训练完成!")
