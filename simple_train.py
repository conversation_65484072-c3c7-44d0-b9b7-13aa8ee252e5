import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

from simplified_transformer import TimeSeriesTransformer, AdvancedTimeSeriesTransformer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SimpleTimeSeriesDataset(Dataset):
    """简化的时间序列数据集"""
    def __init__(self, data: np.ndarray, time_features: np.ndarray, 
                 seq_len: int = 96, pred_len: int = 24, target_col: int = 1):
        self.data = data
        self.time_features = time_features
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.target_col = target_col
        
    def __len__(self):
        return len(self.data) - self.seq_len - self.pred_len + 1
    
    def __getitem__(self, idx):
        # 输入序列
        seq_x = self.data[idx:idx + self.seq_len]
        seq_y = self.data[idx + self.seq_len:idx + self.seq_len + self.pred_len, self.target_col]
        
        # 时间特征
        seq_x_time = self.time_features[idx:idx + self.seq_len]
        
        return {
            'seq_x': torch.FloatTensor(seq_x),
            'seq_y': torch.FloatTensor(seq_y),
            'seq_x_time': torch.FloatTensor(seq_x_time)
        }

class SimplePredictor:
    """简化的预测器"""
    def __init__(self, input_dim: int, time_feature_dim: int, seq_len: int = 96, 
                 pred_len: int = 24, d_model: int = 256, n_heads: int = 8, 
                 n_layers: int = 4, d_ff: int = 1024, dropout: float = 0.1,
                 learning_rate: float = 0.001, batch_size: int = 32):
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        
        # 设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 模型
        self.model = AdvancedTimeSeriesTransformer(
            input_dim=input_dim,
            time_feature_dim=time_feature_dim,
            d_model=d_model,
            n_heads=n_heads,
            n_encoder_layers=n_layers,
            n_decoder_layers=max(1, n_layers // 2),
            d_ff=d_ff,
            seq_len=seq_len,
            pred_len=pred_len,
            dropout=dropout
        ).to(self.device)
        
        self.optimizer = optim.AdamW(self.model.parameters(), lr=learning_rate, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, patience=5, factor=0.5)
        self.criterion = nn.MSELoss()
        
        # 数据预处理器
        self.feature_scaler = StandardScaler()
        
        # 打印模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"总参数数量: {total_params:,}")
        print(f"可训练参数数量: {trainable_params:,}")
    
    def prepare_data(self, df: pd.DataFrame, target_col: str = 'power', 
                     train_ratio: float = 0.7, val_ratio: float = 0.2):
        """准备数据"""
        # 选择特征列
        feature_cols = ['wind_speed', 'power', 'rotor_speed', 'blade_angle']
        time_feature_cols = ['hour_sin', 'hour_cos', 'day_sin', 'day_cos', 
                           'month_sin', 'month_cos']
        
        # 确保所有需要的列都存在
        available_features = [col for col in feature_cols if col in df.columns]
        available_time_features = [col for col in time_feature_cols if col in df.columns]
        
        print(f"可用特征: {available_features}")
        print(f"可用时间特征: {available_time_features}")
        
        # 提取特征和目标
        features = df[available_features].values
        time_features = df[available_time_features].values
        
        # 获取目标列索引
        target_idx = available_features.index(target_col)
        
        # 数据标准化
        features_scaled = self.feature_scaler.fit_transform(features)
        
        # 分割数据
        n_samples = len(features_scaled)
        train_end = int(n_samples * train_ratio)
        val_end = int(n_samples * (train_ratio + val_ratio))
        
        # 创建数据集
        train_dataset = SimpleTimeSeriesDataset(
            features_scaled[:train_end], time_features[:train_end], 
            self.seq_len, self.pred_len, target_idx
        )
        
        val_dataset = SimpleTimeSeriesDataset(
            features_scaled[train_end:val_end], time_features[train_end:val_end], 
            self.seq_len, self.pred_len, target_idx
        )
        
        test_dataset = SimpleTimeSeriesDataset(
            features_scaled[val_end:], time_features[val_end:], 
            self.seq_len, self.pred_len, target_idx
        )
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=self.batch_size, 
                                shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=self.batch_size, 
                              shuffle=False, drop_last=True)
        test_loader = DataLoader(test_dataset, batch_size=self.batch_size, 
                               shuffle=False, drop_last=False)
        
        return train_loader, val_loader, test_loader
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch in train_loader:
            seq_x = batch['seq_x'].to(self.device)
            seq_y = batch['seq_y'].to(self.device)
            seq_x_time = batch['seq_x_time'].to(self.device)
            
            self.optimizer.zero_grad()
            
            # 前向传播
            outputs = self.model(seq_x, seq_x_time)
            
            # 计算损失
            loss = self.criterion(outputs.squeeze(-1), seq_y)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        return total_loss / num_batches
    
    def validate(self, val_loader):
        """验证模型"""
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in val_loader:
                seq_x = batch['seq_x'].to(self.device)
                seq_y = batch['seq_y'].to(self.device)
                seq_x_time = batch['seq_x_time'].to(self.device)
                
                # 前向传播
                outputs = self.model(seq_x, seq_x_time)
                
                # 计算损失
                loss = self.criterion(outputs.squeeze(-1), seq_y)
                total_loss += loss.item()
                
                # 收集预测和真实值
                all_predictions.extend(outputs.squeeze(-1).cpu().numpy().flatten())
                all_targets.extend(seq_y.cpu().numpy().flatten())
        
        avg_loss = total_loss / len(val_loader)
        r2 = r2_score(all_targets, all_predictions)
        
        return avg_loss, r2
    
    def train(self, train_loader, val_loader, epochs: int = 100, patience: int = 15):
        """训练模型"""
        train_losses = []
        val_losses = []
        val_r2_scores = []
        best_val_loss = float('inf')
        patience_counter = 0
        best_model_state = None
        
        print("开始训练...")
        for epoch in range(epochs):
            # 训练
            train_loss = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_r2 = self.validate(val_loader)
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            train_losses.append(train_loss)
            val_losses.append(val_loss)
            val_r2_scores.append(val_r2)
            
            print(f"Epoch {epoch+1}/{epochs} - "
                  f"Train Loss: {train_loss:.6f}, "
                  f"Val Loss: {val_loss:.6f}, "
                  f"Val R²: {val_r2:.4f}, "
                  f"LR: {self.optimizer.param_groups[0]['lr']:.6f}")
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                best_model_state = self.model.state_dict().copy()
            else:
                patience_counter += 1
            
            if patience_counter >= patience:
                print(f"早停在第 {epoch+1} 轮，最佳验证损失: {best_val_loss:.6f}")
                break
        
        # 加载最佳模型
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)
        
        return {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'val_r2_scores': val_r2_scores,
            'best_val_loss': best_val_loss
        }
    
    def predict(self, test_loader):
        """预测"""
        self.model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in test_loader:
                seq_x = batch['seq_x'].to(self.device)
                seq_y = batch['seq_y'].to(self.device)
                seq_x_time = batch['seq_x_time'].to(self.device)
                
                # 前向传播
                outputs = self.model(seq_x, seq_x_time)
                
                # 收集预测和真实值
                all_predictions.extend(outputs.squeeze(-1).cpu().numpy())
                all_targets.extend(seq_y.cpu().numpy())
        
        return np.array(all_predictions), np.array(all_targets)
    
    def evaluate(self, predictions, targets):
        """评估模型性能"""
        pred_flat = predictions.flatten()
        target_flat = targets.flatten()
        
        mse = mean_squared_error(target_flat, pred_flat)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(target_flat, pred_flat)
        r2 = r2_score(target_flat, pred_flat)
        mape = np.mean(np.abs((target_flat - pred_flat) / (target_flat + 1e-8))) * 100
        
        return {
            'MSE': mse,
            'RMSE': rmse,
            'MAE': mae,
            'R²': r2,
            'MAPE': mape
        }

def main():
    """主函数"""
    print("=" * 60)
    print("简化Transformer时间序列预测模型")
    print("=" * 60)
    
    # 加载数据
    print("加载数据...")
    df = pd.read_csv('preprocessed_wind_data.csv', index_col=0, parse_dates=True)
    print(f"数据形状: {df.shape}")
    
    # 模型配置
    config = {
        'seq_len': 96,
        'pred_len': 24,
        'd_model': 256,
        'n_heads': 8,
        'n_layers': 4,
        'd_ff': 1024,
        'dropout': 0.1,
        'learning_rate': 0.001,
        'batch_size': 32
    }
    
    print("\n模型配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 创建预测器
    predictor = SimplePredictor(
        input_dim=4,  # wind_speed, power, rotor_speed, blade_angle
        time_feature_dim=6,  # hour_sin, hour_cos, day_sin, day_cos, month_sin, month_cos
        **config
    )
    
    # 准备数据
    print("\n准备数据...")
    train_loader, val_loader, test_loader = predictor.prepare_data(df)
    
    print(f"训练集批次数: {len(train_loader)}")
    print(f"验证集批次数: {len(val_loader)}")
    print(f"测试集批次数: {len(test_loader)}")
    
    # 训练模型
    print("\n开始训练...")
    history = predictor.train(train_loader, val_loader, epochs=100, patience=15)
    
    # 在测试集上评估
    print("\n在测试集上评估...")
    predictions, targets = predictor.predict(test_loader)
    metrics = predictor.evaluate(predictions, targets)
    
    print("\n测试集评估结果:")
    print("=" * 40)
    for metric, value in metrics.items():
        print(f"{metric}: {value:.6f}")
    
    # 检查是否达到精度要求
    r2_score = metrics['R²']
    if r2_score >= 0.9:
        print(f"\n✅ 模型达到精度要求! R² = {r2_score:.4f} >= 0.9")
    else:
        print(f"\n❌ 模型未达到精度要求. R² = {r2_score:.4f} < 0.9")
    
    # 绘制结果
    plt.figure(figsize=(15, 10))
    
    # 训练历史
    plt.subplot(2, 3, 1)
    plt.plot(history['train_losses'], label='训练损失')
    plt.plot(history['val_losses'], label='验证损失')
    plt.title('训练历史')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 3, 2)
    plt.plot(history['val_r2_scores'], label='验证R²', color='green')
    plt.title('验证R²分数')
    plt.xlabel('Epoch')
    plt.ylabel('R² Score')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 预测结果
    plt.subplot(2, 3, 3)
    sample_idx = np.random.choice(len(predictions), 1)[0]
    plt.plot(targets[sample_idx], label='真实值', alpha=0.8)
    plt.plot(predictions[sample_idx], label='预测值', alpha=0.8)
    plt.title(f'预测样本 {sample_idx+1}')
    plt.xlabel('时间步')
    plt.ylabel('功率 (标准化)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 散点图
    plt.subplot(2, 3, 4)
    pred_flat = predictions.flatten()
    target_flat = targets.flatten()
    plt.scatter(target_flat, pred_flat, alpha=0.5, s=1)
    plt.plot([target_flat.min(), target_flat.max()], 
            [target_flat.min(), target_flat.max()], 'r--', lw=2)
    plt.xlabel('真实值')
    plt.ylabel('预测值')
    plt.title(f'预测 vs 真实 (R² = {r2_score:.4f})')
    plt.grid(True, alpha=0.3)
    
    # 误差分布
    plt.subplot(2, 3, 5)
    errors = pred_flat - target_flat
    plt.hist(errors, bins=50, alpha=0.7, edgecolor='black')
    plt.title('预测误差分布')
    plt.xlabel('误差')
    plt.ylabel('频次')
    plt.grid(True, alpha=0.3)
    
    # 时间步误差
    plt.subplot(2, 3, 6)
    step_errors = []
    for step in range(predictor.pred_len):
        step_pred = predictions[:, step]
        step_target = targets[:, step]
        step_mse = np.mean((step_pred - step_target) ** 2)
        step_errors.append(step_mse)
    
    plt.plot(range(1, predictor.pred_len + 1), step_errors, 'o-')
    plt.title('不同预测步长的MSE')
    plt.xlabel('预测步长')
    plt.ylabel('MSE')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('simple_transformer_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保存模型
    torch.save({
        'model_state_dict': predictor.model.state_dict(),
        'config': config,
        'metrics': metrics,
        'feature_scaler': predictor.feature_scaler
    }, 'simple_transformer_model.pth')
    
    print("\n模型已保存到 'simple_transformer_model.pth'")
    
    return predictor, metrics

if __name__ == "__main__":
    predictor, metrics = main()
